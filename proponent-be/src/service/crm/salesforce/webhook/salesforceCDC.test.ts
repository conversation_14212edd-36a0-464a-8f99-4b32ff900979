import { config } from 'dotenv';
import { cdcSubscriber } from './cdcSubscriber';
import { initializeSalesforceCDCSubscriptions, getCDCHealthStatus } from './initializeSalesforceCDCSubscriptions';

config({ path: '.env.test' });

describe('Salesforce CDC Integration Tests', () => {
  const testTenantId = 1;

  beforeAll(() => {
    console.log('Setting up Salesforce CDC integration tests');
    console.log(`Test tenant ID: ${testTenantId}`);
  });

  afterAll(async () => {
    console.log('Cleaning up CDC subscriptions');
    await cdcSubscriber.stopAllSubscriptions();
  });

  describe('CDC Subscriber', () => {
    it('should initialize CDC subscriber without errors', () => {
      expect(cdcSubscriber).toBeDefined();
      expect(typeof cdcSubscriber.startCDCSubscription).toBe('function');
      expect(typeof cdcSubscriber.stopCDCSubscription).toBe('function');
      expect(typeof cdcSubscriber.isHealthy).toBe('function');
    });

    it('should handle CDC subscription lifecycle', async () => {
      await expect(cdcSubscriber.startCDCSubscription(testTenantId)).resolves.not.toThrow();

      const activeSubscriptions = cdcSubscriber.getActiveSubscriptions();
      console.log(`Active subscriptions: ${activeSubscriptions.length}`);

      await expect(cdcSubscriber.stopCDCSubscription(testTenantId)).resolves.not.toThrow();
    }, 30000);
  });

  describe('CDC Initialization Service', () => {
    it('should initialize CDC subscriptions for active tenants', async () => {
      await expect(initializeSalesforceCDCSubscriptions()).resolves.not.toThrow();

      const healthStatus = getCDCHealthStatus();
      console.log('CDC Health Status:', healthStatus);
    }, 60000);
  });

  describe('CDC Event Processing', () => {
    it('should process mock CDC contact creation event', async () => {
      const mockCDCEvent = {
        payload: {
          ChangeEventHeader: {
            changeType: 'CREATE',
            entityName: 'Contact',
            recordIds: ['003XXXXXXXXXXXXXXX'],
            transactionKey: '12345-67890',
            commitTimestamp: **********,
            commitUser: '005XXXXXXXXXXXXXXX',
            changeOrigin: 'com/salesforce/api/soap/ClientService',
            changedFields: [],
            nulledFields: [],
          },
          Id: '003XXXXXXXXXXXXXXX',
          FirstName: 'Test',
          LastName: 'Contact',
          Email: '<EMAIL>',
        },
        organizationId: 'test-org-id',
      };

      // Test event transformation
      const subscriber = cdcSubscriber as any; // Access private methods for testing
      const transformedEvent = await subscriber.transformCDCEvent(mockCDCEvent, testTenantId);
      
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.eventType).toBe('created');
      expect(transformedEvent.subject.attributes.type).toBe('Contact');
      expect(transformedEvent.subject.Id).toBe('003XXXXXXXXXXXXXXX');
      expect(transformedEvent.tenantId).toBe(testTenantId);
      expect(transformedEvent.changeEventHeader).toBeDefined();
      expect(transformedEvent.changeEventHeader.entityName).toBe('Contact');
      expect(transformedEvent.changeEventHeader.changeType).toBe('CREATE');
      expect(transformedEvent.changeEventHeader.recordIds).toEqual(['003XXXXXXXXXXXXXXX']);
    });

    it('should process mock CDC contact update event with changedFields', async () => {
      const mockCDCUpdateEvent = {
        payload: {
          ChangeEventHeader: {
            changeType: 'UPDATE',
            entityName: 'Contact',
            recordIds: ['003XXXXXXXXXXXXXXX'],
            transactionKey: '12345-67891',
            commitTimestamp: 1234567891,
            commitUser: '005XXXXXXXXXXXXXXX',
            changeOrigin: 'com/salesforce/api/soap/ClientService',
            changedFields: ['Email', 'Title'],
            nulledFields: [],
          },
          // Only changed fields are included in UPDATE events
          Email: '<EMAIL>',
          Title: 'Senior Developer',
        },
        organizationId: 'test-org-id',
      };

      // Test event transformation
      const subscriber = cdcSubscriber as any; // Access private methods for testing
      const transformedEvent = await subscriber.transformCDCEvent(mockCDCUpdateEvent, testTenantId);
      
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.eventType).toBe('updated');
      expect(transformedEvent.subject.Email).toBe('<EMAIL>');
      expect(transformedEvent.subject.Title).toBe('Senior Developer');
      expect(transformedEvent.changeEventHeader.changedFields).toEqual(['Email', 'Title']);
    });

    it('should process mock OpportunityContactRole association event', async () => {
      const mockAssociationEvent = {
        payload: {
          ChangeEventHeader: {
            changeType: 'CREATE',
            entityName: 'OpportunityContactRole',
            recordIds: ['00KXXXXXXXXXXXXXXX'],
            transactionKey: '12345-67892',
            commitTimestamp: 1234567892,
            commitUser: '005XXXXXXXXXXXXXXX',
            changeOrigin: 'com/salesforce/api/soap/ClientService',
            changedFields: [],
            nulledFields: [],
          },
          Id: '00KXXXXXXXXXXXXXXX',
          ContactId: '003XXXXXXXXXXXXXXX',
          OpportunityId: '006XXXXXXXXXXXXXXX',
          Role: 'Decision Maker',
        },
        organizationId: 'test-org-id',
      };

      // Test event transformation
      const subscriber = cdcSubscriber as any; // Access private methods for testing
      const transformedEvent = await subscriber.transformCDCEvent(mockAssociationEvent, testTenantId);
      
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.eventType).toBe('created');
      expect(transformedEvent.subject.attributes.type).toBe('OpportunityContactRole');
      expect(transformedEvent.subject.ContactId).toBe('003XXXXXXXXXXXXXXX');
      expect(transformedEvent.subject.OpportunityId).toBe('006XXXXXXXXXXXXXXX');
    });
  });

  describe('CDC Health Monitoring', () => {
    it('should provide health status information', () => {
      const healthStatus = getCDCHealthStatus();
      expect(typeof healthStatus).toBe('object');

      console.log('Current health status:', healthStatus);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid tenant ID gracefully', async () => {
      const invalidTenantId = -1;

      await expect(cdcSubscriber.startCDCSubscription(invalidTenantId)).resolves.not.toThrow();

      const isHealthy = cdcSubscriber.isHealthy(invalidTenantId);
      expect(isHealthy).toBe(false);
    });
  });
});

/**
 * Manual Testing Instructions:
 * 
 * 1. Ensure you have a Salesforce org with CDC enabled
 * 2. Update testTenantId to match a tenant with Salesforce integration
 * 3. Run: npm test -- salesforceCDC.test.ts
 * 
 * For end-to-end testing:
 * 1. Start the application with CDC enabled
 * 2. Create a contact in your Salesforce org
 * 3. Check application logs for CDC event processing
 * 4. Verify the contact appears in your system with proper associations
 * 
 * Expected CDC Event Flow:
 * 1. Contact created in Salesforce
 * 2. CDC event received via streaming API
 * 3. Event transformed to webhook format
 * 4. Processed through existing contact creation services
 * 5. Persona profile created and meeting associations added
 */
