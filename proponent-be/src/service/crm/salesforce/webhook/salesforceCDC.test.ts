import { config } from 'dotenv';
import { cdcSubscriber } from './cdcSubscriber';
import { initializeSalesforceCDCSubscriptions, getCDCHealthStatus } from './initializeSalesforceCDCSubscriptions';

config({ path: '.env.test' });

describe('Salesforce CDC Integration Tests - Real Event Scenarios', () => {
  const testTenantId = 2; // Based on your actual tenant ID

  beforeAll(() => {
    console.log('Setting up Salesforce CDC integration tests');
    console.log(`Test tenant ID: ${testTenantId}`);
  });

  afterAll(async () => {
    console.log('Cleaning up CDC subscriptions');
    await cdcSubscriber.stopAllSubscriptions();
  });

  describe('RCDC Event Processing', () => {
    it('should process contact creation with full data (email + name)', async () => {
      const realCDCEvent = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          LastModifiedDate: "2025-08-01T07:33:32.000Z",
          Email: "<EMAIL>",
          Name: {
            FirstName: "Sobebar",
            LastName: "Ali",
            Salutation: "Mr."
          },
          OwnerId: "005aj00000JoyXVAAZ",
          CreatedById: "005aj00000JoyXVAAZ",
          Title: "Sobebar",
          CleanStatus: "Pending",
          ChangeEventHeader: {
            commitNumber: "1754033612931719200",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "CREATE",
            changedFields: [],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "000148c5-fd77-f027-e849-5a03089344da",
            commitTimestamp: 1754033612000,
            recordIds: ["003aj00000U5SC5AAN"]
          },
          CreatedDate: "2025-08-01T07:33:32.000Z",
          LastModifiedById: "005aj00000JoyXVAAZ"
        },
        event: {
          replayId: 234107
        }
      };


      const transformedEvent = await cdcSubscriber.processCDCEvent(realCDCEvent, testTenantId);

      console.log
      
      // Verify transformation
      // expect(transformedEvent).toBeDefined();
      // expect(transformedEvent.eventType).toBe('created');
      // expect(transformedEvent.subject.attributes.type).toBe('Contact');
      // expect(transformedEvent.subject.Id).toBe('003aj00000U5SC5AAN');
      // expect(transformedEvent.subject.Email).toBe('<EMAIL>');
      // expect(transformedEvent.subject.FirstName).toBe('Sobebar');
      // expect(transformedEvent.subject.LastName).toBe('Ali');
      // expect(transformedEvent.subject.Salutation).toBe('Mr.');
      // expect(transformedEvent.subject.Title).toBe('Sobebar');
      // expect(transformedEvent.subject.OwnerId).toBe('005aj00000JoyXVAAZ');
      // expect(transformedEvent.tenantId).toBe(testTenantId);
      // expect(transformedEvent.changeEventHeader.changeType).toBe('CREATE');
      // expect(transformedEvent.changeEventHeader.changedFields).toEqual([]);
    });

    it('should process contact creation without email (edge case)', async () => {
      // Real CDC event from your logs - contact without email
      const noEmailCDCEvent = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          LastModifiedDate: "2025-08-01T07:38:27.000Z",
          Name: {
            FirstName: "SObebar",
            LastName: "2"
          },
          OwnerId: "005aj00000JoyXVAAZ",
          CreatedById: "005aj00000JoyXVAAZ",
          Title: "yo",
          CleanStatus: "Pending",
          ChangeEventHeader: {
            commitNumber: "1754033907260260400",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "CREATE",
            changedFields: [],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "000000f4-6b12-67a4-7e24-8d081dec5827",
            commitTimestamp: 1754033907000,
            recordIds: ["003aj00000U5SlZAAV"]
          },
          CreatedDate: "2025-08-01T07:38:27.000Z",
          LastModifiedById: "005aj00000JoyXVAAZ"
        },
        event: {
          replayId: 234108
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(noEmailCDCEvent, testTenantId);
      
      // Verify transformation handles missing email
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.eventType).toBe('created');
      expect(transformedEvent.subject.Id).toBe('003aj00000U5SlZAAV');
      expect(transformedEvent.subject.Email).toBeUndefined();
      expect(transformedEvent.subject.FirstName).toBe('SObebar');
      expect(transformedEvent.subject.LastName).toBe('2');
      expect(transformedEvent.subject.Salutation).toBeUndefined(); // No salutation in this event
      expect(transformedEvent.subject.Title).toBe('yo');
    });

    it('should process contact update with partial Name object', async () => {
      // Real CDC event from your logs - update with only LastName in Name object
      const partialNameUpdateEvent = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          LastModifiedDate: "2025-08-01T07:42:10.000Z",
          Name: {
            LastName: "Ali"
            // Note: No FirstName or Salutation in this update
          },
          Title: "youu",
          ChangeEventHeader: {
            commitNumber: "1754034130319999000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "UPDATE",
            changedFields: [
              "Title",
              "LastModifiedDate",
              "Name.LastName"
            ],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "000323f8-688d-7744-30fa-60c7e6e020fc",
            commitTimestamp: 1754034130000,
            recordIds: ["003aj00000U5SyTAAV"]
          }
        },
        event: {
          replayId: 234110
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(partialNameUpdateEvent, testTenantId);
      
      // Verify transformation handles partial Name object
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.eventType).toBe('updated');
      expect(transformedEvent.subject.Id).toBe('003aj00000U5SyTAAV');
      expect(transformedEvent.subject.LastName).toBe('Ali');
      expect(transformedEvent.subject.FirstName).toBeUndefined(); // Not in this update
      expect(transformedEvent.subject.Salutation).toBeUndefined(); // Not in this update
      expect(transformedEvent.subject.Title).toBe('youu');
      expect(transformedEvent.changeEventHeader.changedFields).toEqual([
        "Title",
        "LastModifiedDate", 
        "Name.LastName"
      ]);
    });

    it('should process email-only update event', async () => {
      // Real CDC event from your logs - email update only
      const emailUpdateEvent = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          LastModifiedDate: "2025-08-01T07:49:12.000Z",
          Email: "<EMAIL>",
          ChangeEventHeader: {
            commitNumber: "1754034552919933000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "UPDATE",
            changedFields: [
              "Email",
              "LastModifiedDate"
            ],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "0000fb16-16b6-d5f2-3000-ca0cd6acfa38",
            commitTimestamp: *************,
            recordIds: ["003aj00000U5SyTAAV"]
          }
        },
        event: {
          replayId: 234111
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(emailUpdateEvent, testTenantId);
      
      // Verify email-only update transformation
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.eventType).toBe('updated');
      expect(transformedEvent.subject.Id).toBe('003aj00000U5SyTAAV');
      expect(transformedEvent.subject.Email).toBe('<EMAIL>');
      expect(transformedEvent.subject.Name).toBeUndefined(); // No Name object in this update
      expect(transformedEvent.subject.FirstName).toBeUndefined();
      expect(transformedEvent.subject.LastName).toBeUndefined();
      expect(transformedEvent.changeEventHeader.changedFields).toEqual([
        "Email",
        "LastModifiedDate"
      ]);
    });

    it('should handle Name object flattening correctly', async () => {
      // Test the Name object flattening logic specifically
      const eventWithComplexName = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          Name: {
            FirstName: "John",
            LastName: "Doe",
            Salutation: "Dr.",
            MiddleName: "Michael" // Additional field that might exist
          },
          Email: "<EMAIL>",
          ChangeEventHeader: {
            commitNumber: "1754034552919933000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "CREATE",
            changedFields: [],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "test-transaction-key",
            commitTimestamp: *************,
            recordIds: ["003TEST000000001"]
          }
        },
        event: {
          replayId: 999999
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(eventWithComplexName, testTenantId);

      // Verify Name object is properly flattened
      expect(transformedEvent.subject.FirstName).toBe('John');
      expect(transformedEvent.subject.LastName).toBe('Doe');
      expect(transformedEvent.subject.Salutation).toBe('Dr.');
      expect(transformedEvent.subject.Name.MiddleName).toBe('Michael'); // Original Name object preserved
      expect(transformedEvent.subject.Email).toBe('<EMAIL>');
    });

    it('should handle events without Name object (non-Contact entities)', async () => {
      // Test Account creation (no Name object expected)
      const accountEvent = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          Name: "Test Company Inc", // For Account, Name is a string, not object
          Industry: "Technology",
          ChangeEventHeader: {
            commitNumber: "1754034552919933000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Account",
            changeType: "CREATE",
            changedFields: [],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "test-account-key",
            commitTimestamp: *************,
            recordIds: ["001TEST000000001"]
          }
        },
        event: {
          replayId: 999998
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(accountEvent, testTenantId);

      // Verify Account event is not affected by Contact Name flattening logic
      expect(transformedEvent.subject.attributes.type).toBe('Account');
      expect(transformedEvent.subject.Name).toBe('Test Company Inc'); // String, not object
      expect(transformedEvent.subject.FirstName).toBeUndefined();
      expect(transformedEvent.subject.LastName).toBeUndefined();
      expect(transformedEvent.subject.Industry).toBe('Technology');
    });
  });

  describe('CDC Event Processing Edge Cases', () => {
    it('should handle missing recordIds gracefully', async () => {
      const eventWithoutRecordIds = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          Id: "003FALLBACK00001", // Fallback ID
          Email: "<EMAIL>",
          ChangeEventHeader: {
            commitNumber: "1754034552919933000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "CREATE",
            changedFields: [],
            recordIds: [], // Empty recordIds array
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "test-fallback-key",
            commitTimestamp: *************
          }
        },
        event: {
          replayId: 999997
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(eventWithoutRecordIds, testTenantId);

      // Should fall back to payload.Id
      expect(transformedEvent.subject.Id).toBe('003FALLBACK00001');
    });

    it('should handle malformed Name object', async () => {
      const eventWithMalformedName = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          Name: null, // Malformed Name object
          Email: "<EMAIL>",
          ChangeEventHeader: {
            commitNumber: "1754034552919933000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "Contact",
            changeType: "CREATE",
            changedFields: [],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "test-malformed-key",
            commitTimestamp: *************,
            recordIds: ["003MALFORMED001"]
          }
        },
        event: {
          replayId: 999996
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(eventWithMalformedName, testTenantId);

      // Should handle null Name gracefully
      expect(transformedEvent.subject.Id).toBe('003MALFORMED001');
      expect(transformedEvent.subject.Email).toBe('<EMAIL>');
      expect(transformedEvent.subject.FirstName).toBeUndefined();
      expect(transformedEvent.subject.LastName).toBeUndefined();
      expect(transformedEvent.subject.Name).toBeNull();
    });

    it('should handle OpportunityContactRole events', async () => {
      // Test OpportunityContactRole CDC event
      const opportunityContactRoleEvent = {
        schema: "pCE9pKTnNeLLWSx3Cxv9Gw",
        payload: {
          ContactId: "003aj00000U5SC5AAN",
          OpportunityId: "006aj00000TestOpp",
          Role: "Decision Maker",
          ChangeEventHeader: {
            commitNumber: "1754034552919933000",
            commitUser: "005aj00000JoyXVAAZ",
            sequenceNumber: 1,
            entityName: "OpportunityContactRole",
            changeType: "CREATE",
            changedFields: [],
            changeOrigin: "com/salesforce/api/soap/64.0;client=SfdcInternalAPI/",
            transactionKey: "test-opp-contact-role",
            commitTimestamp: *************,
            recordIds: ["00Kaj00000TestRole"]
          }
        },
        event: {
          replayId: 999995
        }
      };

      const subscriber = cdcSubscriber as any;
      const transformedEvent = await subscriber.transformCDCEvent(opportunityContactRoleEvent, testTenantId);

      // Verify OpportunityContactRole transformation
      expect(transformedEvent.subject.attributes.type).toBe('OpportunityContactRole');
      expect(transformedEvent.subject.ContactId).toBe('003aj00000U5SC5AAN');
      expect(transformedEvent.subject.OpportunityId).toBe('006aj00000TestOpp');
      expect(transformedEvent.subject.Role).toBe('Decision Maker');
      expect(transformedEvent.eventType).toBe('created');
    });
  });

  describe('CDC Subscription Management', () => {
    it('should initialize CDC subscriptions for active tenants', async () => {
      await expect(initializeSalesforceCDCSubscriptions()).resolves.not.toThrow();

      const healthStatus = getCDCHealthStatus();
      console.log('CDC Health Status:', healthStatus);
    }, 60000);

    it('should provide health status information', () => {
      const healthStatus = getCDCHealthStatus();
      expect(typeof healthStatus).toBe('object');

      console.log('Current health status:', healthStatus);
    });

    it('should handle subscription lifecycle', async () => {
      await expect(cdcSubscriber.startCDCSubscription(testTenantId)).resolves.not.toThrow();

      const activeSubscriptions = cdcSubscriber.getActiveSubscriptions();
      console.log(`Active subscriptions: ${activeSubscriptions.length}`);

      await expect(cdcSubscriber.stopCDCSubscription(testTenantId)).resolves.not.toThrow();
    }, 30000);

    it('should handle invalid tenant ID gracefully', async () => {
      const invalidTenantId = -1;

      await expect(cdcSubscriber.startCDCSubscription(invalidTenantId)).resolves.not.toThrow();

      const isHealthy = cdcSubscriber.isHealthy(invalidTenantId);
      expect(isHealthy).toBe(false);
    });
  });

  describe('CDC Event Validation', () => {
    it('should validate required CDC event structure', async () => {
      const subscriber = cdcSubscriber as any;

      // Test with minimal valid structure
      const minimalEvent = {
        payload: {
          ChangeEventHeader: {
            entityName: "Contact",
            changeType: "CREATE",
            recordIds: ["003MINIMAL0001"]
          }
        }
      };

      const transformedEvent = await subscriber.transformCDCEvent(minimalEvent, testTenantId);
      expect(transformedEvent).toBeDefined();
      expect(transformedEvent.subject.Id).toBe('003MINIMAL0001');
    });

    it('should handle missing ChangeEventHeader gracefully', async () => {
      const subscriber = cdcSubscriber as any;

      const eventWithoutHeader = {
        payload: {
          Id: "003NOHEADER001",
          Email: "<EMAIL>"
          // Missing ChangeEventHeader
        }
      };

      // This should throw an error or handle gracefully
      await expect(subscriber.transformCDCEvent(eventWithoutHeader, testTenantId)).rejects.toThrow();
    });
  });
});

/**
 * Manual Testing Instructions:
 *
 * 1. Ensure you have a Salesforce org with CDC enabled
 * 2. Update testTenantId to match a tenant with Salesforce integration
 * 3. Run: npm test -- salesforceCDC.test.ts
 *
 * For end-to-end testing:
 * 1. Start the application with CDC enabled
 * 2. Create a contact in your Salesforce org
 * 3. Check application logs for CDC event processing
 * 4. Verify the contact appears in your system with proper associations
 *
 * Expected CDC Event Flow:
 * 1. Contact created in Salesforce
 * 2. CDC event received via streaming API
 * 3. Event transformed to webhook format
 * 4. Processed through existing contact creation services
 * 5. Persona profile created and meeting associations added
 *
 * Key Test Scenarios Covered:
 * - Contact creation with full data (email + name)
 * - Contact creation without email (edge case)
 * - Contact updates with partial Name object
 * - Email-only updates
 * - Name object flattening for Contact entities
 * - Non-Contact entities (Account) handling
 * - Missing recordIds fallback
 * - Malformed Name object handling
 * - OpportunityContactRole events
 * - CDC subscription lifecycle
 * - Event validation and error handling
 */
