import { supabaseAdminClient } from '@/db/supabaseClient';
import { DatabaseResponseError } from '@/errors/databaseResponseError';
import crypto from 'crypto';
import { Request } from 'express';

const verifySalesforceSignature = (signature: string, body: string, organizationId: string): boolean => {
  const clientSecret = process.env.SALESFORCE_CLIENT_SECRET;
  if (!clientSecret) return false;

  // Salesforce uses SHA-256 HMAC with organization ID + body
  const rawString = organizationId + body;
  const hashedString = crypto.createHmac('sha256', clientSecret).update(rawString).digest('base64');

  // Use timing-safe comparison
  try {
    return crypto.timingSafeEqual(Buffer.from(hashedString), Buffer.from(signature));
  } catch (error) {
    console.error('Error in signature comparison:', error);
    return false;
  }
};

const validateSalesforceWebhook = (req: Request): { isValid: boolean; events: any[] } => {
  const { body, headers } = req;

  // Parse headers needed to validate signature
  const signatureHeader = headers['x-salesforce-signature'] as string;
  const organizationIdHeader = headers['x-salesforce-organization-id'] as string;

  if (!signatureHeader || !organizationIdHeader) {
    console.log('Missing required Salesforce headers');
    return { isValid: false, events: [] };
  }

  // Create the body string exactly as Salesforce expects
  const bodyString = JSON.stringify(body);

  console.log('Organization ID:', organizationIdHeader);
  console.log('Body string:', bodyString);

  const isValidSignature = verifySalesforceSignature(signatureHeader, bodyString, organizationIdHeader);

  if (isValidSignature) {
    console.log('Signature matches! Salesforce request is valid.');
    return { isValid: true, events: Array.isArray(body) ? body : [body] };
  } else {
    console.log('Signature does not match: Salesforce request is invalid');
    return { isValid: false, events: [] };
  }
};

const getTenantIdsFromOrganizationId = async (organizationId: string): Promise<number[]> => {
  const { data, error } = await supabaseAdminClient.from('tokens_tenant').select('tenantId: tenant_id').eq('external_id', organizationId).eq('type', 'salesforce');

  if (error) {
    console.error('Error getting tenant ID:', error);
    throw new DatabaseResponseError('Error getting tenant ID:', error);
  }
  if (!data || !data.length) {
    console.error('No tenant ID found for organization ID:', organizationId);
    throw new DatabaseResponseError('No tenant ID found for organization ID:', organizationId);
  }
  return data.map((tenant) => tenant.tenantId);
};

const salesforceWebhookHandlerUtils = {
  validateSalesforceWebhook,
  getTenantIdsFromOrganizationId,
};

export default salesforceWebhookHandlerUtils;
