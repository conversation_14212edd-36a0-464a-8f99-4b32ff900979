import { supabaseAdminClient } from '@/db/supabaseClient';
import { DatabaseResponseError } from '@/errors/databaseResponseError';
import { cdcSubscriber } from './cdcSubscriber';

interface SalesforceTenant {
  tenantId: number;
}

/**
 * Initialize Salesforce CDC subscriptions for all active tenants
 * This should be called on application startup
 */
export const initializeSalesforceCDCSubscriptions = async (): Promise<void> => {
  try {
    console.log('Initializing Salesforce CDC subscriptions...');

    // Get all tenants with active Salesforce integrations
    const salesforceTenants = await getActiveSalesforceTenants();

    if (salesforceTenants.length === 0) {
      console.log('No active Salesforce tenants found. Skipping CDC initialization.');
      return;
    }

    console.log(`Found ${salesforceTenants.length} active Salesforce tenants. Starting CDC subscriptions...`);

    // Start CDC subscriptions for each tenant
    const subscriptionPromises = salesforceTenants.map(async (tenant) => {
      try {
        await cdcSubscriber.startCDCSubscription(tenant.tenantId);
        console.log(`CDC subscription started for tenant ${tenant.tenantId}`);
      } catch (error) {
        console.error(`Failed to start CDC subscription for tenant ${tenant.tenantId}:`, error);
        // Don't throw - continue with other tenants
      }
    });

    // Wait for all subscriptions to complete (or fail)
    await Promise.allSettled(subscriptionPromises);

    const activeSubscriptions = cdcSubscriber.getActiveSubscriptions();
    console.log(`Salesforce CDC initialization complete. Active subscriptions: ${activeSubscriptions.length}`);

    if (activeSubscriptions.length > 0) {
      console.log('Active CDC subscriptions:', activeSubscriptions);
    }

  } catch (error) {
    console.error('Error initializing Salesforce CDC subscriptions:', error);
    throw error;
  }
};

/**
 * Get all tenants with active Salesforce integrations
 */
async function getActiveSalesforceTenants(): Promise<SalesforceTenant[]> {
  try {
    const { data, error } = await supabaseAdminClient
      .from('tokens_tenant')
      .select('tenant_id')
      .eq('type', 'salesforce')
      .not('access_token', 'is', null)
      .not('refresh_token', 'is', null);

    if (error) {
      throw new DatabaseResponseError('Error fetching Salesforce tenants', error);
    }

    return (data || []).map((row) => ({
      tenantId: row.tenant_id,
    }));
  } catch (error) {
    console.error('Error fetching active Salesforce tenants:', error);
    throw error;
  }
}

/**
 * Add a new tenant to CDC subscriptions
 * Call this when a new Salesforce integration is added
 */
export const addTenantToCDC = async (tenantId: number): Promise<void> => {
  try {
    console.log(`Adding tenant ${tenantId} to CDC subscriptions`);
    await cdcSubscriber.startCDCSubscription(tenantId);
    console.log(`Successfully added tenant ${tenantId} to CDC subscriptions`);
  } catch (error) {
    console.error(`Failed to add tenant ${tenantId} to CDC subscriptions:`, error);
    throw error;
  }
};

/**
 * Remove a tenant from CDC subscriptions
 * Call this when a Salesforce integration is removed
 */
export const removeTenantFromCDC = async (tenantId: number): Promise<void> => {
  try {
    console.log(`Removing tenant ${tenantId} from CDC subscriptions`);
    await cdcSubscriber.stopCDCSubscription(tenantId);
    console.log(`Successfully removed tenant ${tenantId} from CDC subscriptions`);
  } catch (error) {
    console.error(`Failed to remove tenant ${tenantId} from CDC subscriptions:`, error);
    throw error;
  }
};

/**
 * Gracefully shutdown all CDC subscriptions
 * Call this on application shutdown
 */
export const shutdownSalesforceCDC = async (): Promise<void> => {
  try {
    console.log('Shutting down Salesforce CDC subscriptions...');
    await cdcSubscriber.stopAllSubscriptions();
    console.log('Salesforce CDC shutdown complete');
  } catch (error) {
    console.error('Error during Salesforce CDC shutdown:', error);
    throw error;
  }
};

/**
 * Get health status of CDC subscriptions
 */
export const getCDCHealthStatus = (): { [tenantId: number]: boolean } => {
  const activeSubscriptions = cdcSubscriber.getActiveSubscriptions();
  const healthStatus: { [tenantId: number]: boolean } = {};

  // Extract tenant IDs from subscription keys and check health
  activeSubscriptions.forEach(subscriptionKey => {
    const tenantId = parseInt(subscriptionKey.split('-')[0]);
    if (!isNaN(tenantId)) {
      healthStatus[tenantId] = cdcSubscriber.isHealthy(tenantId);
    }
  });

  return healthStatus;
};

export const salesforceCDCServices = {
  initializeSalesforceCDCSubscriptions,
  addTenantToCDC,
  removeTenantFromCDC,
  shutdownSalesforceCDC,
  getCDCHealthStatus,
};