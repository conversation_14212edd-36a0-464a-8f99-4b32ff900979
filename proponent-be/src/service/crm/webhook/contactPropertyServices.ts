import { supabaseAdminClient } from '@/db/supabaseClient';
import { getCRMClient } from '@/service/crm/getCrmClient';
import { contactsProfileDBServices } from '@/service/contact-profile/db/contactProfileDBServices';
import { crmContactServices } from '@/service/crm/contact/contactServices';
import { interactionAssociationsDBService } from '@/service/crm-associations/meeting-associations/interactionAssociationsDBService';
import { handlePersonaProfileCreation } from '@/service/persona-profile/handlePersonaProfileCreation';
import { validateEmailForWebhook } from '@/service/utils/validateContactForWebhook';

interface IHandleEmailPropertyChange {
  email: string;
  tenantId: number;
  contactId: string;
}

const handleEmailPropertyChange = async ({ email, tenantId, contactId }: IHandleEmailPropertyChange) => {
  // Validate email and get meeting IDs
  const { isValid, meetingIds } = await validateEmailForWebhook({
    email,
    tenantId,
    supabase: supabaseAdminClient,
  });

  if (!isValid) return;

  // Get existing contact associations (by contactId, not email)
  const existingMeetingIds = await interactionAssociationsDBService.getMeetingIdsForEntity({
    supabase: supabaseAdminClient,
    tenantId,
    crmEntityType: 'contact',
    crmId: contactId,
  });

  // Get contact's current email and update profile if exists
  const crmClient = await getCRMClient({ supabase: supabaseAdminClient, tenantId });

  await handlePersonaProfileCreation({
    email,
    tenantId,
    supabase: supabaseAdminClient,
    crm: crmClient,
    hasInsights: true,
    skipValidation: true,
  });

  // Add new associations for meetings with new email that don't already exist
  if (meetingIds.length > 0) {
    const newAssociationPromises = meetingIds.map(async (meetingId) => {
      const alreadyAssociated = existingMeetingIds.includes(meetingId);

      if (!alreadyAssociated) {
        return interactionAssociationsDBService.createInteractionAssociation({
          supabase: supabaseAdminClient,
          tenantId,
          interactionId: meetingId,
          interactionType: 'meeting',
          crmEntityType: 'contact',
          crmId: contactId,
          ruleVersion: 'v1.0',
        });
      }
      return null;
    });

    await Promise.all(newAssociationPromises.filter(Boolean));
  }

  console.log(`Updated email for contact ${contactId} to ${email}`);
};

interface IHandleJobTitlePropertyChange {
  jobTitle: string;
  tenantId: number;
  contactId: string;
}

const handleJobTitlePropertyChange = async ({ jobTitle, tenantId, contactId }: IHandleJobTitlePropertyChange) => {
  if (!jobTitle) return;

  const crmClient = await getCRMClient({ supabase: supabaseAdminClient, tenantId });
  const contact = await crmContactServices.getCRMContactById({ crm: crmClient, id: contactId });
  const email = contact?.email;
  if (!email) return;

  const contactProfile = await contactsProfileDBServices.getContactProfile({
    supabase: supabaseAdminClient,
    email,
    tenantId,
  });
  if (!contactProfile) return;

  await contactsProfileDBServices.updateContactProfile({
    supabase: supabaseAdminClient,
    tenantId,
    email,
    data: { job_title: jobTitle },
  });
};

export const crmWebhookContactPropertyServices = { handleEmailPropertyChange, handleJobTitlePropertyChange };
