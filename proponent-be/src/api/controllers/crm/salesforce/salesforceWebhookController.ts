import { Request, Response } from 'express';

import salesforceWebhookHandlerUtils from '@/service/crm/salesforce/webhook/utils';
import {
  handleCompanyAssociationChange,
  handleContactAssociationChange,
  handleContactPropertyChange,
  handleContactCreation,
  handleCompanyCreation,
} from '@/service/crm/salesforce/webhook/salesforceWebhookServices';

const salesforceWebhookHandler = async (req: Request, res: Response) => {
  try {
    const { isValid, events } = salesforceWebhookHandlerUtils.validateSalesforceWebhook(req);

    if (!isValid) {
      console.log('Salesforce webhook validation failed');
      console.log(req.headers);
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Respond immediately to prevent retries (matches HubSpot pattern)
    res.status(200).json({ message: 'Salesforce webhook received successfully' });

    // Process events asynchronously (fire and forget - matches HubSpot)
    processWebhookEvents(events);

    return;
  } catch (error) {
    console.error('Error processing Salesforce webhook:', error);
    // Always respond with 200 to prevent retries (matches HubSpot)
    if (!res.headersSent) return res.status(200).json({ message: 'Salesforce webhook processed with errors' });

    return;
  }
};

// Separate async function to process events without blocking response (matches HubSpot exactly)
const processWebhookEvents = async (events: any[]) => {
  for (const event of events) {
    try {
      // Map Salesforce event types to handlers
      switch (event.eventType) {
        case 'created':
          if (event.sobject?.attributes?.type === 'Contact')
            handleContactCreation(event); // No await (matches HubSpot)
          else if (event.sobject?.attributes?.type === 'Account') handleCompanyCreation(event); // No await (matches HubSpot)

          break;
        case 'updated':
          if (event.sobject?.attributes?.type === 'Contact') handleContactPropertyChange(event); // No await (matches HubSpot)

          break;
        case 'associated':
        case 'disassociated':
          if (event.relationshipName?.includes('Contact'))
            handleContactAssociationChange(event); // No await (matches HubSpot)
          else if (event.relationshipName?.includes('Account')) handleCompanyAssociationChange(event); // No await (matches HubSpot)

          break;
        default:
          console.log('Unhandled Salesforce webhook event:', event.eventType);
      }
    } catch (error) {
      console.error(`Error processing Salesforce webhook event ${event.eventType}:`, error);
      // Continue with next event - don't let one failure stop others (matches HubSpot)
    }
  }
};

export const salesforceWebhookController = { salesforceWebhookHandler };
