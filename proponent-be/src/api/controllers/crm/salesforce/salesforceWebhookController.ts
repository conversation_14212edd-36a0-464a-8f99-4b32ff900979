/**
 * IMPORTANT: Salesforce does NOT support traditional webhooks like HubSpot.
 * This controller is kept for testing purposes only and is NOT used in production.
 * 
 * Production uses Change Data Capture (CDC) streaming API instead.
 * See: /src/service/crm/salesforce/webhook/cdcSubscriber.ts
 */

import { Request, Response } from 'express';

import salesforceWebhookHandlerUtils from '@/service/crm/salesforce/webhook/utils';
import {
  handleCompanyAssociationChange,
  handleContactAssociationChange,
  handleContactPropertyChange,
  handleContactCreation,
  handleCompanyCreation,
} from '@/service/crm/salesforce/webhook/salesforceWebhookServices';

const salesforceWebhookHandler = async (req: Request, res: Response) => {
  try {
    const { isValid, events } = salesforceWebhookHandlerUtils.validateSalesforceWebhook(req);

    if (!isValid) {
      console.log('Salesforce webhook validation failed');
      console.log(req.headers);
      return res.status(401).json({ error: 'Invalid signature' });
    }

    res.status(200).json({ message: 'Salesforce webhook received successfully' });

    processWebhookEvents(events);

    return;
  } catch (error) {
    console.error('Error processing Salesforce webhook:', error);
    // Always respond with 200 to prevent retries (matches HubSpot)
    if (!res.headersSent) return res.status(200).json({ message: 'Salesforce webhook processed with errors' });

    return;
  }
};

const processWebhookEvents = async (events: any[]) => {
  for (const event of events) {
    try {
      // Map Salesforce event types to handlers
      switch (event.eventType) {
        case 'created':
          if (event.sobject?.attributes?.type === 'Contact')
            handleContactCreation(event); // No await (matches HubSpot)
          else if (event.sobject?.attributes?.type === 'Account') handleCompanyCreation(event);

          break;
        case 'updated':
          if (event.sobject?.attributes?.type === 'Contact') handleContactPropertyChange(event); // No await (matches HubSpot)

          break;
        case 'associated':
        case 'disassociated':
          if (event.relationshipName?.includes('Contact'))
            handleContactAssociationChange(event); // No await (matches HubSpot)
          else if (event.relationshipName?.includes('Account')) handleCompanyAssociationChange(event); // No await (matches HubSpot)

          break;
        default:
          console.log('Unhandled Salesforce webhook event:', event.eventType);
      }
    } catch (error) {
      console.error(`Error processing Salesforce webhook event ${event.eventType}:`, error);
      // Continue with next event - don't let one failure stop others (matches HubSpot)
    }
  }
};

export const salesforceWebhookController = { salesforceWebhookHandler };
