import { Router } from 'express';

import { contactsController } from '../controllers/crm/contactsController';
import { authenticateRequest } from '@/middleware/authenticationMiddleware';
import { dealsController } from '../controllers/crm/dealsController';
import { dealOwnersController } from '../controllers/crm/ownersController';
import { emailController } from '../controllers/crm/emailController';
import { crmOauthController } from '../controllers/crm/crmOauthController';
import { notesController } from '../controllers/crm/notesController';
import crmAuthMiddleware from '@/middleware/crmAuthMiddleware';
import { companiesController } from '../controllers/crm/companiesController';
import { portalController } from '../controllers/crm/portalController';
import { crmAssociationsController } from '../controllers/crm/crmAssociationsController';
import { hubspotWebhookController } from '../controllers/crm/hubspot/hubspotWebhookController';
import { salesforceWebhookController } from '../controllers/crm/salesforce/salesforceWebhookController';

const router = Router();

// oauths
router.use('/hubspot/oauth/install', authenticateRequest, crmOauthController.getHubspotOauthUrl);
router.use('/hubspot/oauth/callback', crmOauthController.hubspotOauthCallback);
router.use('/salesforce/oauth/install', authenticateRequest, crmOauthController.getSalesforceOauthUrl);
router.use('/salesforce/oauth/callback', crmOauthController.salesforceOauthCallback);

//associations routes
router.post('/associations/email', authenticateRequest, crmAuthMiddleware, crmAssociationsController.getCRMEmailAssociations);
router.get('/associations/deal/:id', authenticateRequest, crmAuthMiddleware, crmAssociationsController.getCRMDealAssociations);
router.get('/associations/contact/:id', authenticateRequest, crmAuthMiddleware, crmAssociationsController.getCRMContactAssociations);
router.get('/associations/company/:id', authenticateRequest, crmAuthMiddleware, crmAssociationsController.getCRMCompanyAssociations);

//specific routes
router.get('/contacts/deal-insight/:dealId', authenticateRequest, crmAuthMiddleware, contactsController.getDealInsightContacts);
router.get('/contacts/company-insight/:companyId', authenticateRequest, crmAuthMiddleware, contactsController.getCompanyInsightContacts);

router.post('/contacts/search', authenticateRequest, crmAuthMiddleware, contactsController.searchCRMContacts);
router.post('/contacts/emails', authenticateRequest, crmAuthMiddleware, contactsController.getCRMContactsByEmails);
router.get('/contacts/analyzed', authenticateRequest, crmAuthMiddleware, contactsController.getCRMContactsAnalyzed);
router.get('/companies/analyzed', authenticateRequest, crmAuthMiddleware, companiesController.getCompaniesAnalyzed);
router.get('/contacts/:id', authenticateRequest, crmAuthMiddleware, contactsController.getCRMContactById);
router.post('/contacts/batch', authenticateRequest, crmAuthMiddleware, contactsController.getCRMContactBatchByIds);
router.get('/companies/:id', authenticateRequest, crmAuthMiddleware, companiesController.getCompanyById);
router.post('/companies/ids', authenticateRequest, crmAuthMiddleware, companiesController.getCompaniesByIds);
router.get('/deals/:id', authenticateRequest, crmAuthMiddleware, dealsController.getDealById);
router.get('/deals', authenticateRequest, crmAuthMiddleware, dealsController.getDealsAnalyzed);
router.post('/deals/ids', authenticateRequest, crmAuthMiddleware, dealsController.getDealsByIds);
router.get('/portal-id', authenticateRequest, crmAuthMiddleware, portalController.getPortalId);
router.get('/pipelines', authenticateRequest, crmAuthMiddleware, dealsController.getPipelines);
router.post('/notes', authenticateRequest, crmAuthMiddleware, notesController.upsertChecklistNote);
router.get('/owners', authenticateRequest, crmAuthMiddleware, dealOwnersController.getDealOwners);
router.post('/emails', authenticateRequest, crmAuthMiddleware, emailController.getEmails);

//hubspot webhook
router.post('/hubspot/webhook', hubspotWebhookController.hubspotWebhookHandler);

//salesforce webhook
router.post('/salesforce/webhook', salesforceWebhookController.salesforceWebhookHandler);

export default router;
